/* Custom Navigation Styles */
header {
    position: absolute;
    z-index: 999999;
    width: 100%;
    height: 80px;
    transition: all 0.3s ease;
    background-color: transparent;
    top: 0;
    left: 0;
}

header .nav-link {
    color: #fff;
    transition: color 0.3s ease;
}

header.background-header {
    background-color: #fff !important;
}

header.background-header .nav-link {
    color: #1e1e1e !important;
}

.Modern-Slider {
    position: relative;
    margin: 0 !important;
    padding: 0 !important;
    top: 0 !important;
}

.main-banner {
    position: relative;
    margin: 0 !important;
    padding: 0 !important;
    top: 0 !important;
    height: 100vh;
}

.main-banner .header-text {
    margin: 0 !important;
    padding: 0 !important;
}

.background-header {
    position: fixed !important;
    background-color: #fff !important;
    box-shadow: 0px 1px 10px rgba(0,0,0,0.1) !important;
    top: 0;
    left: 0;
    height: 80px;
    width: 100%;
    z-index: 999999;
    transition: all 0.5s ease;
}

header:not(.background-header) {
    transition: all 0.5s ease;
}

.Modern-Slider {
    position: relative;
    z-index: 1;
}

.main-banner {
    position: relative;
    z-index: 1;
}

/* Rule removed as it conflicts with JavaScript logic for non-home page header styling.
body:not(.home) header {
    background-color: rgba(0, 0, 0, 0.85);
    position: fixed;
}
*/

header.transparent {
    background-color: transparent !important;
}

.navbar {
    position: relative;
    z-index: 999999;
}

#navbarResponsive,
.nav-item,
.nav-link,
.navbar-brand {
    position: relative;
    z-index: 999999;
}

.Modern-Slider {
    position: relative;
    z-index: 1;
}

body.home header {
    position: absolute;
    background-color: transparent;
}

.navbar {
    padding: 10px 0;
    transition: all 0.3s ease;
}

.background-header {
    background-color: #fff !important;
    box-shadow: 0px 1px 10px rgba(0,0,0,0.1);
    position: fixed !important;
    top: 0;
    left: 0;
    animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 999999 !important;
}

.nav-link {
    transition: color 0.4s ease !important;
}

.background-header .nav-link,
.background-header .navbar-brand h2 {
    color: #1e1e1e !important;
    transition: color 0.4s ease;
}

header.background-header .navbar-nav .nav-link {
    color: #1e1e1e !important;
}

body:not(.home) header .navbar-nav .nav-link {
    color: #1e1e1e !important;
}

header.transparent {
    background-color: transparent !important;
}

.background-header.show {
    opacity: 1;
    visibility: visible;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
    }
    to {
        transform: translateY(0);
    }
}

.background-header .nav-link {
    color: #1e1e1e !important;
}

.background-header .nav-link:hover,
.background-header .nav-item.active .nav-link {
    color: #0069FF !important;
}

.navbar-brand {
    padding: 0;
    margin: 0;
}

.navbar-brand h2 {
    color: #fff;
    transition: color 0.3s ease;
}

.background-header .navbar-brand h2 {
    color: #0069FF !important;
}

#navbarResponsive {
    display: flex !important;
    justify-content: flex-end;
}

.navbar-nav {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
}

.nav-item {
    margin: 0 12px;
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-item:first-child {
    margin-left: 0;
}

.nav-item:last-child {
    margin-right: 0;
}

.navbar-collapse {
    height: 100%;
    align-items: center;
}

.nav-link {
    padding: 8px 12px !important;
    font-size: 13px !important;
    color: #fff;
    font-weight: 300;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    position: relative;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover,
.nav-item.active .nav-link {
    color: #0069FF !important;
}

.nav-link:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #0069FF;
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover:after,
.nav-item.active .nav-link:after {
    transform: scaleX(1);
}

.navbar-toggler {
    display: none !important;
}

@media (max-width: 768px) {
    .navbar-nav {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .nav-item {
        margin: 0 4px;
    }
    
    .nav-link {
        padding: 8px 8px !important;
        font-size: 12px !important;
    }
}
