/* TemplateMo - Finance Business

https://templatemo.com/tm-545-finance-business

*/

body {
    font-family: 'Poppins', sans-serif;
    overflow-x: hidden;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
p {
    margin-bottom: 0px;
    font-size: 14px;
    color: #666666;
    line-height: 30px;
}
a {
    text-decoration: none!important;
}
ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

h1,h2,h3,h4,h5,h6 {
    margin: 0px;
}

a.filled-button {
    background-color: #a4c639;
    color: #fff;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 12px 30px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
}

a.filled-button:hover {
    background-color: #fff;
    color: #a4c639;
}

a.border-button {
    background-color: transparent;
    font-size: 1.2rem;
    padding: 16px 36px;
    color: #fff;
    border: 2px solid #fff;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 10px 28px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
}

a.border-button:hover {
    background-color: #fff;
    color: #a4c639;
}

.section-heading {
    text-align: center;
    margin-bottom: 60px;
}

.section-heading h2 {
    font-size: 36px;
    font-weight: 600;
    color: #1e1e1e;
}

.section-heading em {
    font-style: normal;
    color: #a4c639;
}

.section-heading span {
    display: block;
    margin-top: 15px;
    text-transform: uppercase;
    font-size: 15px;
    color: #666;
    letter-spacing: 1px;
}

#preloader {
    overflow: hidden;
    background: #a4c639;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    position: fixed;
    z-index: 9999999;
    color: #fff;
}

#preloader .jumper {
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: block;
    position: absolute;
    margin: auto;
    width: 50px;
    height: 50px;
}

#preloader .jumper > div {
    background-color: #fff;
    border-radius: 100%;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    position: absolute;
    opacity: 0;
    width: 10px;
    height: 10px;
    -webkit-animation: jumper 1s 0s linear infinite;
    animation: jumper 1s 0s linear infinite;
}

#preloader .jumper > div:nth-child(2) {
    -webkit-animation-delay: 0.33333s;
    animation-delay: 0.33333s;
}

#preloader .jumper > div:nth-child(3) {
    -webkit-animation-delay: 0.66666s;
    animation-delay: 0.66666s;
}

@-webkit-keyframes jumper {
    0% {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    5% {
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 0;
    }
}

@keyframes jumper {
    0% {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
    }
    5% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

/* Sub Header Style */

.sub-header {
    background-color: #a4c639;
    height: 46px;
    line-height: 46px;
    display: block;
    position: relative; /* Ensure z-index is effective */
    z-index: 100000; /* Very high z-index to bring it to the front */
    /* margin-top: 40px; */ /* Reverted: Increased to lower the sub-header further */
}

body {
margin: 0;
padding: 0;
}

.main-banner {
position: relative;
margin: 0;
padding: 0;
}

.Modern-Slider {
position: relative;
margin: 0;
padding: 0;
}

.Modern-Slider .item .img-fill {
    padding: 0;
    margin: 0;
    background-position: center;
    background-size: cover;
    height: 100vh;
}

.main-banner.header-text {
    margin: 0;
    padding: 0;
    position: relative;
}

/* Header Style */
header {
    position: absolute;
    top: 46px; /* Reverted: Height of .sub-header */
    z-index: 99999; /* High, but allow sub-header to be under if it had z-index */
    width: 100%;
    background-color: transparent !important; /* Initially transparent */
    height: auto;
    -webkit-transition: all 0.3s ease-in-out 0s;
    -moz-transition: all 0.3s ease-in-out 0s;
    -o-transition: all 0.3s ease-in-out 0s;
    transition: all 0.3s ease-in-out 0s;
}

.Modern-Slider .item .img-fill {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    height: 100vh;
}

body {
margin: 0;
padding: 0;
overflow-x: hidden;
}

html {
margin: 0;
padding: 0;
}

.Modern-Slider {
    margin: 0;
    padding: 0;
    position: relative;
    top: 0;
    width: 100%;
    height: 100vh;
}

.main-banner {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    position: relative;
}

.Modern-Slider .item {
    height: 100vh;
    overflow: hidden;
}

.main-banner.header-text {
    position: relative;
    padding-top: 0;
    width: 100%;
    text-align: center;
    z-index: 1;
}

.Modern-Slider .item {
margin: 0;
padding: 0;
}

.Modern-Slider .text-content {
    padding-top: 30px;
    position: relative;
    z-index: 2;
    text-align: center;
}

.main-banner.header-text {
    /* position: relative; */ /* Handled by new flex rules */
    /* margin-top: 100; */ /* Unitless, potentially problematic, handled by flex */
    /* padding-top: 10; */ /* Unitless, potentially problematic, handled by flex */
}
header .navbar {
    padding: 60px 0px; /* Increased top/bottom padding to 60px for maximum separation */
    display: flex;
    align-items: center;
}
.background-header .navbar {
    padding: 10px 0px;
}
.background-header {
    top: 0;
    position: fixed;
    background-color: #ffffff !important; /* White background on scroll */
    box-shadow: 0px 1px 10px rgba(0,0,0,0.1);
}
.background-header .navbar-brand h2 {
    color: #1e1e1e !important; /* Dark text color on scroll */
}
.background-header .navbar-nav .nav-link:hover,
.background-header .navbar-nav .active>.nav-link,
.background-header .navbar-nav .nav-link.current,
.background-header .navbar-nav .nav-link.show,
.background-header .navbar-nav .show>.nav-link {
    color: #a4c639!important;
    border-bottom: 3px solid #a4c639;
}
.navbar .navbar-brand {
    float: left;
    margin-top: 12px;
    outline: none;
}
.navbar .navbar-brand h2 {
    color: #fff; /* Default white text */
    text-transform: uppercase;
    font-size: 24px;
    font-weight: 700;
    -webkit-transition: all .3s ease 0s;
    -moz-transition: all .3s ease 0s;
    -o-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
}
.navbar .navbar-brand h2 em {
    font-style: normal;
    font-size: 16px;
}
#navbarResponsive {
    z-index: 999;
    display: flex !important;
    justify-content: flex-end;
    width: auto !important;
}
.navbar-collapse {
    text-align: center;
}
.navbar .navbar-nav .nav-item {
    margin: 0px 15px;
}
.navbar .navbar-nav a.nav-link {
    text-transform: capitalize;
    font-size: 15px;
    font-weight: 500;
    letter-spacing: 0.5px;
    color: #fff; /* Default white text */
    transition: all 0.5s;
    margin-top: 5px;
    padding-bottom: 15px;
}
.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .active>.nav-link,
.navbar .navbar-nav .nav-link.current,
.navbar .navbar-nav .nav-link.show,
.navbar .navbar-nav .show>.nav-link {
    color: #a4c639 !important;
    border-bottom: 3px solid #a4c639;
}
.navbar .navbar-toggler-icon {
    background-image: none;
}
.navbar .navbar-toggler {
    border: none;
    color: #fff;
    border-radius: 0;
    cursor: pointer;
    outline: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.8)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
    display: inline-block;
    width: 1.5em;
    height: 1.5em;
    vertical-align: middle;
    content: "";
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
}

.background-header .navbar-toggler-icon {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(164, 198, 57, 0.8)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

/* Banner Style */

.img-fill{
    width: 100%;
    display: block;
    overflow: hidden;
    position: relative;
    text-align: center
}

.img-fill img {
    min-height: 100%;
    min-width: 100%;
    position: relative;
    display: inline-block;
    max-width: none;
}

*,
*:before,
*:after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.Grid1k {
    padding: 0 15px;
    max-width: 1200px;
    margin: auto;
}

.blocks-box,
.slick-slider {
    margin: 0;
    padding: 0!important;
}

.slick-slide {
    float: left;
    padding: 0;
}

.Modern-Slider .item .img-fill {
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    /* height: 100%; */ /* Let flex: 1 in new rules (end of file) handle this dimension */
}

.text-content {
    padding-top: 80px;
    text-align: center;
    position: relative;
    z-index: 10;
}

.Modern-Slider {
    position: relative;
    /* margin-top: -80px; */
}

.Modern-Slider .item {
    position: relative;
}

.Modern-Slider .item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
    z-index: 1;
}

.Modern-Slider .text-content {
    z-index: 2;
    /* position: relative; */ /* Superseded by static positioning in the new flex rules at the end of the file */
    /* margin-top: 30px; */
}

.Modern-Slider .item .img-fill {
    position: relative;
}

.Modern-Slider .item-1 .img-fill {
    background-image: url(../images/slide_01.jpg);
}

.Modern-Slider .item-2 .img-fill {
    background-image: url(../images/slide_02.jpg);
}

.Modern-Slider .item-3 .img-fill {
    background-image: url(../images/slide_03.jpg);
}

.Modern-Slider .NextArrow{
    position:absolute;
    top:50%; /* Changed from 75% to move arrows up */
    right:30px;
    transform: translateY(-50%); /* Added to vertically center the arrow itself */
    border:0 none;
    background-color: transparent;
    text-align:center;
    font-size: 36px;
    font-family: 'FontAwesome';
    color:#FFF;
    z-index:5;
    outline: none;
    cursor: pointer;
}

.Modern-Slider .NextArrow:before{
    content:'\f105';
}

.Modern-Slider .PrevArrow {
    position:absolute;
    top: calc(50% + 2px); /* Adjusted by +2px to lower it slightly */
    left:30px;
    transform: translateY(-50%); /* Added to vertically center the arrow itself */
    border:0 none;
    background-color: transparent;
    text-align:center;
    font-size: 36px;
    font-family: 'FontAwesome';
    color:#FFF;
    z-index:5;
    outline: none;
    cursor: pointer;
}

.Modern-Slider .PrevArrow:before{
    content:'\f104';
}

ul.slick-dots {
    display: none!important;
}

.Modern-Slider .text-content {
    text-align: left; /* Reverted to left */
    /* width: 75%; */ /* Handled by new rules: max-width: 600px; width: 100%; */
    /* position: absolute; */
    /* top: 45%; */
    /* left: 50%; */ /* Handled by parent flex centering */
    /* transform: translate(-50%, -50%); */
    /* display: flex; */ /* Handled by new rules at the end */
    /* flex-direction: column; */ /* Handled by new rules at the end */
    /* gap: 20px; */ /* Handled by new rules at the end */
    /* padding-bottom: 60px; */ /* Handled by new rules (padding: 1rem 0) at the end */
}

@media (max-height: 800px) {
    .Modern-Slider .text-content {
        padding-top: 40px;
    }
}

@media (max-width: 768px) {
    .Modern-Slider .text-content {
        width: 90%; /* Responsive adjustment */
        padding-bottom: 80px; /* Responsive adjustment */
        /* top: 40%; */ /* Obsolete: Positioning now handled by flexbox centering */
    }
    .Modern-Slider .item h4 {
        font-size: 22px;
        margin-bottom: 10px;
    }
    .Modern-Slider .item p {
        font-size: 13px;
        margin-bottom: 15px;
        line-height: 1.2;
    }
    .Modern-Slider .item a.filled-button {
        padding: 6px 12px;
        font-size: 12px;
        margin-top: 10px;
    }
}
.Modern-Slider .text-content .filled-button {
    margin-top: 20px;
    display: inline-block;
    max-width: fit-content;
    background-color: #a4c639;
}

.Modern-Slider .item h6 {
    /* margin-bottom: 10px; */ /* Handled by gap in .text-content (new rules) */
    /* margin-top: 120px; */ /* Removed to allow flex centering and gap to work (new rules) */
    font-size: 10px;
    font-size: 22px;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 1px;
    color:#a4c639;
    animation:fadeOutRight 1s both;
}

.Modern-Slider .item h4 {
    /* margin-bottom: 30px; */ /* Handled by gap in .text-content (new rules) */
    text-transform: uppercase;
    font-size: 28px; /* Reduced from 38px */
    font-weight: 700;
    letter-spacing: 2.5px;
    color:#FFF;
    overflow:hidden;
    animation:fadeOutLeft 1s both;
}

.Modern-Slider .item p {
    max-width: 700px;
    color: #fff;
    font-size: 15px;
    font-weight: 400;
    line-height: 1.4;
    /* margin-bottom: 30px; */ /* Handled by gap in .text-content (new rules) */
}

.Modern-Slider .item a.filled-button {
    display: inline-block;
    background-color: #a4c639;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 5px;
    margin: 15px 0 10px 0; /* Further reduced bottom margin */
    transition: all 0.3s;
    text-decoration: none;
    position: relative;
    z-index: 100;
}

.Modern-Slider .item a.filled-button:hover {
    background-color: #fff;
    color: #a4c639;
}

.Modern-Slider .item.slick-active h6{
    animation:fadeInDown 1s both 1s;
}

.Modern-Slider .item.slick-active h4{
    animation:fadeInLeft 1s both 1.5s;
}

.Modern-Slider .item.slick-active{
    animation:Slick-FastSwipeIn 1s both;
}

.Modern-Slider .buttons {
    position: relative;
}

/*
.Modern-Slider {
    background:#000;
    margin-top: 0;
    position: relative;
    z-index: 1;
    /* min-height: calc(100vh - 80px); */ /* Handled by new rules at the end */
    /* height: calc(100vh - 80px); */ /* Handled by new rules at the end */
/* } */ /* This block is largely redundant with the "v4" rules at the end of the file */

/*
.main-banner.header-text {
    position: relative;
    padding-top: 40px;
    overflow: hidden;
    /* height: 100vh; */ /* Handled by new flex rules */
/* } */ /* This block is superseded by the "v4" rules at the end of the file, especially padding-top */

.main-banner .Modern-Slider {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
}

.Modern-Slider .NextArrow,
.Modern-Slider .PrevArrow {
    z-index: 2;
}

/* ==== Slick Slider Css Ruls === */
.slick-slider{position:relative;display:block;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-touch-callout:none;-khtml-user-select:none;-ms-touch-action:pan-y;touch-action:pan-y;-webkit-tap-highlight-color:transparent}
.slick-list{position:relative;display:block;overflow:hidden;margin:0;padding:0}
.slick-list:focus{outline:none}.slick-list.dragging{cursor:hand}
.slick-slider .slick-track,.slick-slider .slick-list{-webkit-transform:translate3d(0,0,0);-ms-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}
.slick-track{position:relative;top:0;left:0;display:block}
.slick-track:before,.slick-track:after{display:table;content:''}.slick-track:after{clear:both}
.slick-loading .slick-track{visibility:hidden}
.slick-slide{display:none;float:left /* If RTL Make This Right */ ;height:100%;min-height:1px}
.slick-slide.dragging img{pointer-events:none}
.slick-initialized .slick-slide{display:block}
.slick-loading .slick-slide{visibility:hidden}
.slick-vertical .slick-slide{display:block;height:auto;border:1px solid transparent}

/* Request Form */

.request-form {
    background-color: #a4c639;
    padding: 40px 0px; /* Original padding, provides top/bottom space for the section */
    color: #fff;
    /* margin-top: 80px; Removed to bring it closer to the slider */
    width: 100%;       /* Full width section block */
}

.request-form .container {
    display: flex; /* To vertically center the child .row within the container's height */
    align-items: center; /* To vertically center the child .row */
    /* Let Bootstrap's .container class handle width, max-width, padding, and margin for horizontal centering. */
}

.request-form .row {
    display: flex; /* Make the row a flex container */
    align-items: center; /* Vertically center the columns (col-md-8 and col-md-4) within the row */
    width: 100%; /* Ensure the row takes the full width of its parent container */
}

/* The following .left-content, h4, span, .border-button rules
   are general styles for .request-form. Some might be overridden by more specific rules later,
   or apply if the HTML structure differs from the primary "Request a call back" section.
   The .left-content class is not used in the primary "Request a call back" HTML.
*/
.request-form .left-content {
    display: flex;
    align-items: baseline;
}

.request-form h4 { /* General h4 style within .request-form */
    margin: 0;
    font-size: 20px; /* Overridden by more specific rule below for the main text */
    line-height: 1;
}

.request-form span { /* General span style within .request-form */
    /* This margin-left: 15px will be overridden for the main subtext by the more specific rule below */
    margin: 0 0 0 15px;
    font-size: 14px; /* Overridden by more specific rule below for the main subtext */
}

.request-form .border-button { /* Style for the button if it's directly in .request-form, not the main one */
    margin: 0;
    padding: 8px 20px;
}

.Modern-Slider {
    margin-bottom: 0; /* Reverted to original 0 */
    position: relative;
    z-index: 2;
}

.services {
    padding-top: 60px;
    margin-top: 20px !important;
    position: relative;
    z-index: 1;
}

.services .container {
    padding-top: 40px;
}

.services,
.fun-facts,
.more-info,
.testimonials {
    margin-top: 80px !important;
}

/* Specific styles for the "Request a call back right now ?" text elements */
.request-form h4 { /* Targets the h4 in the col-md-8 */
    font-family: 'Poppins', sans-serif;
    color: #fff; /* Explicitly white, though inherited */
    font-size: 28px; /* Increased for impact */
    font-weight: 700; /* Bolder */
    line-height: 1.4; /* Improved line spacing */
    text-align: left;
    margin-bottom: 15px; /* Space below h4 */
}

.request-form h4 .highlight-black {
    color: #000000; /* Black color for "my code." */
    font-size: inherit; /* Explicitly inherit font size from h4 */
    font-weight: inherit; /* Explicitly inherit font weight from h4 */
    display: inline; /* Ensure it stays on the same line */
    /* Other properties like font-family, line-height will also be inherited */
}

.request-form span { /* Targets the span subtext in the col-md-8 */
    display: block;
    margin-top: 10px;
    margin-left: 0; 
    text-align: left;
    /* font-size and font-weight will be controlled by the strong tag inside */
}

.request-form span strong {
    font-family: 'Poppins', sans-serif;
    color: #000000 !important; /* Matches inline style, !important to override if needed */
    font-size: 18px !important; /* Explicit size, matches 1.3em of ~14px base, !important to override */
    font-weight: 500; /* Poppins Medium */
    line-height: 1.6;
    display: inline-block; /* Ensures it behaves as a block for styling */
}

.request-form a.border-button {
    margin-top: 0; /* Adjusted for baseline alignment */
    float: right;
}

/* Services */

.services {
    margin-top: 140px;
}

.service-item img {
    width: 100%;
    overflow: hidden;
}

.service-item .down-content {
    background-color: #f7f7f7;
    padding: 30px;
}

.service-item .down-content h4 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.25px;
    margin-bottom: 15px;
    font-family: 'Poppins', sans-serif; /* Ensure Poppins font */
    text-align: left; /* Ensure left alignment */
}

.service-item .down-content p {
    margin-bottom: 20px;
    font-family: 'Poppins', sans-serif; /* Ensure Poppins font */
    text-align: left; /* Ensure left alignment */
    /* Inherits font-size, color, line-height from global 'p' styles or defaults */
}

/* Fun Facts */

.fun-facts {
    margin-top: 140px;
    background-image: url(../images/fun-facts-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    background-attachment: fixed;
    padding: 140px 0px;
    color: #fff;
}

.fun-facts span {
    text-transform: uppercase;
    font-size: 15px;
    color: #fff;
    letter-spacing: 1px;
    margin-bottom: 10px;
    display: block;
}

.fun-facts h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 35px;
}

.fun-facts em {
    font-style: normal;
    color: #a4c639;
}

.fun-facts p {
    color: #fff;
    margin-bottom: 40px;
}

.fun-facts .left-content {
    margin-right: 45px;
}

.count-area-content {
    text-align: center;
    background-color: rgba(250,250,250,0.1);
    padding: 25px 30px 35px 30px;
    margin: 15px 0px;
}

.count-digit {
    margin: 5px 0px;
    color: #a4c639;
    font-weight: 700;
    font-size: 36px;
}
.count-title {
    font-size: 20px;
    font-weight: 600;
    color: #fff;
    letter-spacing: 0.5px;
}

/* More Info */

.more-info {
    margin-top: 140px;
}

.more-info .left-image img {
    width: 100%;
    overflow: hidden;
}

.more-info .more-info-content {
    background-color: #f7f7f7;
}

.about-info .more-info-content {
    background-color: transparent;
}

.about-info .right-content {
    padding: 0px!important;
    margin-right: 30px;
}

.more-info .right-content {
    padding: 45px 45px 45px 30px;
}

.more-info .right-content span {
    text-transform: uppercase;
    font-size: 15px;
    color: #666;
    letter-spacing: 1px;
    margin-bottom: 10px;
    display: block;
}

.more-info .right-content h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 35px;
}

.more-info .right-content em {
    font-style: normal;
    color: #a4c639;
}

.more-info .right-content p {
    margin-bottom: 30px;
}

/* Testimonials Style */

.about-testimonials {
    margin-top: 0px!important;
}

.testimonials {
    margin-top: 140px;
    background-color: #f7f7f7;
    padding: 140px 0px;
}
.testimonial-item .inner-content {
    text-align: center;
    background-color: #fff; 
    padding: 30px;
    border-radius: 5px;
}
.testimonial-item p {
    font-size: 14px;
    font-weight: 400;
}
.testimonial-item h4 {
    font-size: 19px;
    font-weight: 700;
    color: #1e1e1e;
    letter-spacing: 0.5px;
    margin-bottom: 0px;
}
.testimonial-item span {
    display: inline-block;
    margin-top: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #a4c639;
}
.testimonial-item img {
    max-width: 60px;
    border-radius: 50%;
    margin: 25px auto 0px auto;
}

/* Call Back Style */

.callback-services {
    border-top: 1px solid #eee;
    padding-top: 140px;
}

.contact-us {
    background-color: #f7f7f7;
    padding: 140px 0px;
}

.contact-us .contact-form {
    background-color: transparent!important;
    padding: 0!important;
}

.callback-form {
    margin-top: 140px;
}

.callback-form .contact-form {
    background-color: #a4c639;
    padding: 60px;
    border-radius: 5px;
    text-align: center;
}

.callback-form .contact-form input {
    border-radius: 20px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    padding: 0px 15px;
    color: #6a6a6a;
    font-size: 13px;
    text-transform: none;
    box-shadow: none;
    border: none;
    margin-bottom: 35px;
}

.callback-form .contact-form input:focus {
    outline: none;
    box-shadow: none;
    border: none;
}

.callback-form .contact-form textarea {
    border-radius: 20px;
    height: 120px;
    max-height: 200px;
    min-height: 120px;
    display: inline-block;
    padding: 15px;
    color: #6a6a6a;
    font-size: 13px;
    text-transform: none;
    box-shadow: none;
    border: none;
    margin-bottom: 35px;
}

.callback-form .contact-form textarea:focus {
    outline: none;
    box-shadow: none;
    border: none;
}

.callback-form .contact-form ::-webkit-input-placeholder { /* Edge */
    color: #aaa;
}
.callback-form .contact-form :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #aaa;
}
.callback-form .contact-form ::placeholder {
    color: #aaa;
}

.callback-form .contact-form button.border-button {
    background-color: transparent;
    color: #fff;
    border: 2px solid #fff;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 10px 28px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
    outline: none;
    box-shadow: none;
    text-shadow: none;
    cursor: pointer;
}

.callback-form .contact-form button.border-button:hover {
    background-color: #fff;
    color: #a4c639;
}

.contact-us .contact-form button.filled-button {
    background-color: #a4c639;
    color: #fff;
    border: none;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 12px 30px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
    outline: none;
    box-shadow: none;
    text-shadow: none;
    cursor: pointer;
}

.contact-us .contact-form button.filled-button:hover {
    background-color: #fff;
    color: #a4c639;
}

/* Partners Style */

.contact-partners {
    margin-top: -8px!important;
}

.partners {
    margin-top: 140px;
    background-color: #f7f7f7;
    padding: 60px 0px;
}

.partners .owl-item {
    text-align: center;
    cursor: pointer;
}

.partners .partner-item img {
    max-width: 156px;
    margin: auto;
}

/* Footer Style */

footer {
    background-color: #232323;
    padding: 80px 0px;
    color: #fff;
}

footer h4 {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: 0.25px;
    margin-bottom: 35px;
}
footer p {
    color: #fff;
}

footer ul.social-icons {
    margin-top: 25px;
}

footer ul.social-icons li {
    display: inline-block;
    margin-right: 5px;
}

footer ul.social-icons li:last-child {
    margin-right: 0px;
}

footer ul.social-icons li a {
    width: 34px;
    height: 34px;
    display: inline-block;
    line-height: 34px;
    text-align: center;
    background-color: #fff;
    color: #232323;
    border-radius: 50%;
    transition: all 0.3s;
}

footer ul.social-icons li a:hover {
    background-color: #a4c639;
}

footer ul.menu-list li {
    margin-bottom: 13px;
}

footer ul.menu-list li:last-child {
    margin-bottom: 0px;
}

footer ul.menu-list li a {
    font-size: 14px;
    color: #fff;
    transition: all 0.3s;
}

footer ul.menu-list li a:hover {
    color: #a4c639;
}

footer .contact-form input {
    border-radius: 20px;
    height: 40px;
    line-height: 40px;
    display: inline-block;
    padding: 0px 15px;
    color: #aaa!important;
    background-color: #343434;
    font-size: 13px;
    text-transform: none;
    box-shadow: none;
    border: none;
    margin-bottom: 15px;
}

footer .contact-form input:focus {
    outline: none;
    box-shadow: none;
    border: none;
    background-color: #343434;
}

footer .contact-form textarea {
    border-radius: 20px;
    height: 120px;
    max-height: 200px;
    min-height: 120px;
    display: inline-block;
    padding: 15px;
    color: #aaa!important;
    background-color: #343434;
    font-size: 13px;
    text-transform: none;
    box-shadow: none;
    border: none;
    margin-bottom: 15px;
}

footer .contact-form textarea:focus {
    outline: none;
    box-shadow: none;
    border: none;
    background-color: #343434;
}

footer .contact-form ::-webkit-input-placeholder { /* Edge */
    color: #aaa;
}
footer .contact-form :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: #aaa;
}
footer .contact-form ::placeholder {
    color: #aaa;
}

footer .contact-form button.filled-button {
    background-color: transparent;
    color: #fff;
    background-color: #a4c639;
    border: none;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 700;
    padding: 12px 30px;
    border-radius: 30px;
    display: inline-block;
    transition: all 0.3s;
    outline: none;
    box-shadow: none;
    text-shadow: none;
    cursor: pointer;
}

footer .contact-form button.filled-button:hover {
    background-color: #fff;
    color: #a4c639;
}

/* Sub-footer Style */

.sub-footer {
    background-color: #343434;
    text-align: center;
    padding: 25px 0px;
}

.sub-footer p {
    color: #fff;
    font-weight: 300;
    letter-spacing: 0.5px;
}

.sub-footer a {
    color: #fff;
}

.page-heading {
    text-align: center;
    background-image: url(../images/page-heading-bg.jpg);
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
    padding: 230px 0px 150px 0px;
    color: #fff;
}

.page-heading h1 {
    text-transform: capitalize;
    font-size: 36px;
    font-weight: 700;
    letter-spacing: 2px;
    margin-bottom: 18px;
}

.page-heading span {
    font-size: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #fff;
    display: block;
}

/* team */

.team {
    background-color: #f7f7f7;
    margin-top: 140px;
    margin-bottom: -140px;
    padding: 120px 0px;
}

.team-item img {
    width: 100%;
    overflow: hidden;
}

.team-item .down-content {
    background-color: #fff;
    padding: 30px;
}

.team-item .down-content h4 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.25px;
    margin-bottom: 10px;
}

.team-item .down-content span {
    color: #a4c639;
    font-weight: 600;
    font-size: 14px;
    display: block;
    margin-bottom: 15px;
}

.team-item .down-content p {
    margin-bottom: 20px;
}

/* Single Service */

.single-services {
    margin-top: 140px;
}

#tabs ul {
    margin: 0;
    padding: 0;
}
#tabs ul li {
    margin-bottom: 10px;
    display: inline-block;
    width: 100%;
}
#tabs ul li:last-child {
    margin-bottom: 0px;
}
#tabs ul li a {
    text-transform: capitalize;
    width: 100%;
    padding: 30px 30px;
    display: inline-block;
    background-color: transparent;
    font-size: 20px;
    color: #121212;
    letter-spacing: 0.5px;
    font-weight: 700;
    transition: all 0.3s;
    border-bottom: 3px solid transparent;
}
#tabs ul li a i {
    float: right;
    margin-top: 5px;
}
#tabs ul .ui-tabs-active span {
    background: #000000;
    border: #000000;
    line-height: 90px;
    border-bottom: none;
}
#tabs ul .ui-tabs-active a {
    color: #000000;
    border-bottom: 3px solid #000000;
    background-color: transparent;
}
#tabs ul .ui-tabs-active span {
    color: #1e1e1e;
}
.tabs-content {
    margin-left: 30px;
    text-align: left;
    display: inline-block;
    transition: all 0.3s;
}
.tabs-content img {
    max-width: 100%;
    overflow: hidden;
}
.tabs-content h4 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.25px;
    margin-bottom: 15px;
    margin-top: 30px;
}
.tabs-content p {
    font-size: 14px;
    color: #7a7a7a;
    margin-bottom: 0px;
}

/* Contact Information */

.contact-information {
    margin-top: 140px;
}

.contact-information .contact-item {
    padding: 60px 30px;
    background-color: #f7f7f7;
    text-align: center;
}

.contact-information .contact-item i {
    color: #a4c639;
    font-size: 48px;
    margin-bottom: 40px;
}

.contact-information .contact-item h4 {
    font-size: 20px;
    font-weight: 700;
    letter-spacing: 0.25px;
    margin-bottom: 15px;
}

.contact-information .contact-item p {
    margin-bottom: 20px;
}

.contact-information .contact-item a {
    font-weight: 600;
    color: #a4c639;
    font-size: 15px;
}

/* ===== INICIO: Forzar menú siempre horizontal (Version Corregida y sin duplicados) ===== */
.navbar-toggler {
    display: none !important; /* Oculta permanentemente el botón hamburguesa */
}

#navbarResponsive { /* Este es el ID del div que contiene los elementos del menú */
    display: flex !important; /* Fuerza a que se muestre como un contenedor flex (horizontal por defecto) */
    flex-basis: auto;         /* Permite que su tamaño se base en el contenido */
    visibility: visible !important; /* Asegura que sea visible */

    /* Anula estilos que la plantilla podría aplicar para el modo móvil/colapsado */
    position: static !important; /* Posición por defecto, no absoluta o fija como en el menú móvil */
    background-color: transparent !important; /* Sin fondo blanco que podría tener el menú móvil */
    box-shadow: none !important; /* Sin sombra que podría tener el menú móvil */
    width: auto !important; /* Ancho automático, no el 100% del menú móvil */
    text-align: left !important; /* Alineación de texto como en el escritorio */
    /* Anula posicionamiento 'top' o 'left' si se usaban para el menú desplegable */
    top: auto !important;
    left: auto !important;
    /* Asegura que el menú se alinee a la derecha */
    justify-content: flex-end !important; 
}

.navbar .navbar-nav {
    flex-direction: row !important; /* Asegura que los elementos de la lista estén en fila */
    margin-left: auto !important; /* Empuja el menú hacia la derecha, como en el escritorio */
}

/* Estilos para los elementos individuales del menú, asegurando la apariencia de escritorio */
.navbar .navbar-nav .nav-item {
    margin: 0 10px !important; /* Espaciado horizontal entre elementos (ajusta 10px si necesitas más/menos) */
    border-bottom: none !important; /* Elimina cualquier borde inferior del modo móvil */
}

.navbar .navbar-nav a.nav-link {
    padding: 5px 10px 15px 10px !important; /* top:5, R/L:10, bottom:15 - closer to original structure */
    font-size: 15px !important;    /* Original font size */
    color: #fff;        /* Color de texto para el fondo oscuro del encabezado */
    font-weight: 500 !important;   /* Original font weight */
    text-transform: capitalize;    /* Transformación del texto (original del tema para escritorio) */
    letter-spacing: 0.5px;
    margin-top: 5px !important;    /* Restore original margin-top */
}

/* Estilos para el enlace activo o al pasar el ratón */
.navbar .navbar-nav .nav-link:hover,
.navbar .navbar-nav .active > .nav-link,
.navbar .navbar-nav .nav-link.current,
.navbar .navbar-nav .nav-link.show,
.navbar .navbar-nav .show > .nav-link {
    color: #a4c639 !important; /* Color al pasar el ratón/activo */
    border-bottom: 3px solid #a4c639 !important; /* Subrayado al pasar el ratón/activo (estilo escritorio) */
}
/* ===== FIN: Forzar menú siempre horizontal (Version Corregida y sin duplicados) ===== */

/* Responsive Style */
@media (max-width: 768px) {
    .sub-header {
        display: none;
    }

    .request-form .container {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .request-form .left-content {
        flex-direction: column;
        align-items: center;
    }

    .request-form span {
        margin: 5px 0 0 0;
    }

    .request-form .border-button {
        margin-top: 10px;
    }

    .request-form {
        padding: 30px 0px; /* Adjusted padding for mobile */
        margin-top: 40px;  /* Added margin-top to position it after the slider in mobile view */
        text-align: center; /* Consolidated text-align from the duplicate rule */
    }
    .Modern-Slider .item h6 {
        margin-bottom: 15px;
        font-size: 18px;
    }
    .Modern-Slider .item h4 {
        margin-bottom: 25px;
        font-size: 28px;
        line-height: 36px;
        letter-spacing: 1px;
    }
    .Modern-Slider .item p {
        max-width: 570px;
        line-height: 25px;
        margin-bottom: 30px;
    }
    .Modern-Slider .NextArrow{
        right:5px;
    }
    .Modern-Slider .PrevArrow {
        left:5px;
    }
    /* The duplicate .request-form { text-align: center; } rule is now removed as its content is merged above */
    .request-form a.border-button {
        float: none;
        margin-top: 30px;
    }
    .services .service-item {
        margin-bottom: 30px;
    }
    .fun-facts .left-content {
        margin-right: 0px;
        margin-bottom: 30px;
    }
    .more-info .right-content {
        padding: 30px;
    }
    footer {
        padding: 80px 0px 20px 0px;
    }
    footer .footer-item {
        border-bottom: 1px solid #343434;
        margin-bottom: 30px;
        padding-bottom: 30px;
    }
    footer .last-item {
        border-bottom: none;
    }
    .about-info .right-content {
        margin-right: 0px;
        margin-bottom: 30px;
    }
    .team .team-item {
        margin-bottom: 30px;
    }
    .tabs-content {
        margin-left: 0px;
        margin-top: 30px;
    }
    .contact-item {
        margin-bottom: 30px;
    }
}

/* Responsive Slider Content Styling - v4 - Cleaned & Commented Flexbox Hierarchy */

/* Ensures the viewport can be used as a basis for 100vh calculations */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 1. Main banner container: Establishes the 100vh context minus header height */
/*    - height: 100vh makes it full viewport.
      - padding-top: 80px reserves space for the fixed/absolute header.
      - display: flex (column) allows .Modern-Slider (child) to use flex: 1. */
.main-banner.header-text {
  height: 100vh; /* Adjusted to standard viewport height */
  padding-top: 80px; /* Space for the absolutely positioned header. ADJUST IF HEADER HEIGHT CHANGES. */
  box-sizing: border-box; /* Include padding in the 100vh calculation */
  display: flex; /* Enable flex for its direct child (.Modern-Slider) */
  flex-direction: column; /* Stack children vertically */
  overflow: hidden; /* Prevent content spill */
  position: relative; /* Context for potential absolute children, though not primary for this flex setup */
}

/* 2. Slider main wrapper (.Modern-Slider): Takes up remaining space in banner */
/*    - flex: 1 makes it fill the space left by padding-top in .main-banner.header-text.
      - display: flex (column) allows .slick-list (child) to use flex: 1. */
.Modern-Slider {
  flex: 1; /* Key: Expands to fill available vertical space in .main-banner.header-text */
  display: flex; /* Enable flex for its direct child (.slick-list) */
  flex-direction: column; /* Stack children vertically */
  position: relative; /* For positioning arrows/dots and z-index context */
  overflow: hidden; /* Essential for slider functionality */
  background: #000; /* Original background, if still desired */
  z-index: 1; /* Base z-index for the slider */
}

/* 3. Slick's list wrapper (.slick-list): Takes full height of .Modern-Slider */
/*    - This is a direct child of .Modern-Slider and parent of .slick-track.
      - flex: 1 makes it fill the vertical space within .Modern-Slider. */
.Modern-Slider .slick-list {
  flex: 1; /* Key: Expands to fill available vertical space in .Modern-Slider */
  /* Slick's default styles (position, display:block, overflow) are generally fine. */
  /* height: 100%; was removed as flex:1 is preferred for flex item sizing. */
}

/* 4. Slick's track wrapper (.slick-track): Takes full height of .slick-list */
/*    - This is a direct child of .slick-list and parent of .item/.slick-slide.
      - height: 100% ensures the track (which holds all slides horizontally) is as tall as its container. */
.Modern-Slider .slick-track {
  height: 100%; /* Key: Ensures the track is as tall as .slick-list.
                   Slick's default (display:block, position:relative) handles horizontal movement. */
}

/* 5. Individual slide (.item, which is also .slick-slide): Uses flex to manage .img-fill */
/*    - .slick-slide (which .item is) already has height: 100% from Slick's base CSS.
      - display: flex (column) allows .img-fill (child) to use flex: 1. */
.Modern-Slider .item {
  display: flex; /* Enable flex for its direct child (.img-fill) */
  flex-direction: column; /* Stack children vertically */
  /* height: 100%; is inherited from .slick-slide default CSS, ensuring it fills .slick-track */
  position: relative; /* For the ::before pseudo-element overlay */
  overflow: hidden; /* Prevent content spill from individual slide */
}

/* 6. Image & Text container within slide (.img-fill): Fills .item and centers text */
/*    - flex: 1 makes it fill the vertical space within its parent .item.
      - display: flex with align-items: center centers .text-content vertically. */
.Modern-Slider .item .img-fill {
  flex: 1; /* Key: Expands to fill the entire height of its parent .item */
  display: flex; /* Enable flex for its direct child (.text-content) */
  align-items: center; /* Key: Vertically centers .text-content */
  justify-content: flex-start; /* Reverted to flex-start to allow left padding to control position */
  padding: 0 30px 0 150px; /* Increased left padding to 150px to move content slightly right */
  width: 100%; /* Ensure it uses full width of .item */
  position: relative; /* For background images and layering with ::before */
  /* background-size, background-position, background-repeat are handled by earlier rules for .img-fill */
}

/* 7. Text content block (.text-content): Styled for readability and flow */
/*    - position: static and transform: none ensure it behaves predictably within the flex layout.
      - display: flex (column) with gap manages spacing of internal text elements. */
.Modern-Slider .text-content {
  position: static; /* Key: Ensures it flows within .img-fill, overriding prior absolute/relative */
  transform: none; /* Key: Remove any prior transforms */
  max-width: 600px; /* Constrain text width for readability */
  width: 100%; /* Takes full width available within .img-fill's padding */
  z-index: 2; /* Sits above the .item::before overlay */
  display: flex; /* Enable flex for its children (h6, h4, p, button) */
  flex-direction: column; /* Stack text elements vertically */
  gap: 15px; /* Vertical spacing between text elements */
  padding-top: 80px; /* Increased from 30px to shift the text block further down */
  /* text-align: left; is handled by an earlier rule and is fine */
  /* padding-bottom, etc., are now handled by flex centering (in .img-fill) and gap */
}

/* 8. Button inside .text-content: Specific styling */
.Modern-Slider .text-content .filled-button {
  margin-top: 35px !important; /* Increased from 20px and added !important for diagnostics */
  align-self: flex-start; /* Prevents button from stretching if .text-content had align-items:stretch */
  /* display: inline-block; max-width: fit-content; are fine from earlier rules */
}

/* 9. Overlay on .item: Darkens the background image for text legibility */
.Modern-Slider .item::before {
  content: '';
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 100%);
  z-index: 1; /* Positioned behind .text-content (z-index: 2) */
}

/* Sub Header Style Additions */
.sub-header ul.left-info li a {
    display: flex;
    align-items: center;
    color: #fff;
    font-size: 14px;
    font-weight: 400;
}

.sub-header ul.left-info li i {
    margin-right: 10px;
    color: #fff;
    font-size: 16px;
}

.sub-header ul.right-icons li a {
    color: #fff;
    display: inline-block; /* Ensure the anchor tag has a proper block for clicking */
    padding: 5px; /* Add a little padding to increase clickable area if needed */
    position: relative; /* Add position context for z-index */
    z-index: 100001; /* Ensure links are above sub-header background if needed, and other elements */
    pointer-events: auto !important; /* Force click events to be processed */
}

.sub-header ul.right-icons li a i {
    pointer-events: none; /* Make the icon itself pass clicks through to the parent <a> tag */
}

/* Fix for nav items to match the design */
.navbar .navbar-nav .nav-item.active .nav-link {
    border-bottom: 3px solid #a4c639;
}

/* Fix for sub-header items */
.sub-header ul.left-info li {
    display: inline-flex;
    align-items: center;
    padding: 0px 15px;
}

.sub-header ul.right-icons {
  float: right; /* Aligns the entire list of icons to the right of its container */
}

.sub-header ul.right-icons li {
    display: inline-block;
    margin: 0 12px; /* Increased margin to 12px left/right for more spacing */
}
