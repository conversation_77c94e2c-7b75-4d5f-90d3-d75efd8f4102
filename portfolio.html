<!DOCTYPE html>
<html lang="en">

  <head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="TemplateMo">
    <link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900&display=swap" rel="stylesheet">

    <title>Portfolio - Financial Data Engineering Projects</title>

    <!-- Bootstrap core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Additional CSS Files -->
    <link rel="stylesheet" href="assets/css/fontawesome.css">
    <link rel="stylesheet" href="assets/css/templatemo-finance-business.css">
    <link rel="stylesheet" href="assets/css/owl.css">
    <link rel="stylesheet" href="assets/css/custom-nav.css">
    <link rel="stylesheet" href="assets/css/custom-styles.css">

    <style>
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.5);
      }
      .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 30px;
        border: 1px solid #e0e0e0;
        width: 90%;
        max-width: 800px;
        border-radius: 8px;
        position: relative;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      }
      .close {
        color: #888;
        float: right;
        font-size: 24px;
        font-weight: normal;
        position: absolute;
        top: 20px;
        right: 25px;
        width: 32px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 50%;
        transition: all 0.3s;
        background-color: #f5f5f5;
      }
      .close:hover,
      .close:focus {
        color: #333;
        background-color: #e0e0e0;
        text-decoration: none;
        cursor: pointer;
      }
      .portfolio-item .filled-button {
        background-color: #f0f0f0;
        color: #333;
        padding: 5px 15px;
        font-size: 14px;
        text-transform: none;
        border-radius: 20px;
        display: inline-block;
        margin-top: 15px;
      }
      .modal-content .filled-button {
        background-color: #f0f0f0;
        color: #333;
        padding: 8px 20px;
        font-size: 14px;
        border-radius: 20px;
        text-decoration: none;
        display: inline-block;
        margin-top: 20px;
        transition: all 0.3s;
      }
      .modal-content .filled-button:hover {
        background-color: #e0e0e0;
        color: #222;
      }
      .modal-content h4 {
        margin-bottom: 20px;
        padding-right: 30px;
      }
      .modal-content p {
        margin-bottom: 15px;
        line-height: 1.6;
      }
      .modal-content h5 {
        margin: 25px 0 15px;
      }
      .modal-content ul {
        margin-bottom: 25px;
        padding-left: 20px;
      }
      .modal-content ul li {
        margin-bottom: 8px;
      }
    </style>
  </head>

<body class="home">

    <!-- ***** Preloader Start ***** -->
    <div id="preloader">
        <div class="jumper">
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>  
    <!-- ***** Preloader End ***** -->

    <!-- Header -->
    <div class="sub-header">
      <div class="container">
        <div class="row">
          <div class="col-md-8 col-xs-12">
            <ul class="left-info">
              <li><a href="#"><i class="fa fa-clock-o"></i>Mon-Sun 09:00-21:00</a></li>
              <li><a href="#"><i class="fa fa-phone"></i>+58 4123020280</a></li>
            </ul>
          </div>
          <div class="col-md-4">
            <ul class="right-icons">
              <li><a href="https://linkedin.com" target="_blank"><i class="fa fa-linkedin"></i></a></li>
              <li><a href="https://instagram.com" target="_blank"><i class="fa fa-instagram"></i></a></li>
              <li><a rel="nofollow" href="https://github.com/josetraderx" target="_blank"><i class="fa fa-github"></i></a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <header class="">
      <nav class="navbar navbar-expand-lg">
        <div class="container">
          <a class="navbar-brand" href="index.html"><h2>Finance Business</h2></a>
          <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarResponsive">
            <ul class="navbar-nav ml-auto">
              <li class="nav-item">
                <a class="nav-link" href="index.html">Home
                  <span class="sr-only">(current)</span>
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="about.html">About Me</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="services.html">Services</a>
              </li>
              <li class="nav-item active">
                <a class="nav-link" href="portfolio.html">Portfolio</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="contact.html">Contact</a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </header>

    <!-- Page Content -->
    <div class="page-heading header-text">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <h1>Portfolio</h1>
            <span>Featured projects and case studies in <strong>quantitative finance</strong> and <strong>data engineering</strong></span>
          </div>
        </div>
      </div>
    </div>

    <div class="portfolio-items">
      <div class="container">
        <div class="row">
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/crypto-dashboard.jpg" alt="Crypto Dashboard" class="img-fluid">
              <div class="portfolio-content">
                <h4>Crypto Analytics Dashboard</h4>
                <p>Real-time cryptocurrency trading dashboard with market sentiment analysis and automated trading signals.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>React</li>
                  <li>WebSocket</li>
                  <li>MongoDB</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal1">Learn More</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/portfolio-analytics.jpg" alt="Portfolio Analytics" class="img-fluid">
              <div class="portfolio-content">
                <h4>Portfolio Analytics Engine</h4>
                <p>Advanced portfolio optimization system using modern portfolio theory and machine learning algorithms.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>NumPy</li>
                  <li>Pandas</li>
                  <li>Scikit-learn</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal2">Learn More</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/backtest-results.jpg" alt="Backtesting Framework" class="img-fluid">
              <div class="portfolio-content">
                <h4>Backtesting Framework</h4>
                <p>Custom backtesting engine for evaluating trading strategies with advanced risk metrics and visualizations.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>Pandas</li>
                  <li>Plotly</li>
                  <li>FastAPI</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal3">Learn More</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/ml-dashboard.jpg" alt="Moving Average Trading Bot" class="img-fluid">
              <div class="portfolio-content">
                <h4>Moving Average Trading Bot</h4>
                <p>Automated trading system based on moving average crossover strategies, designed for crypto and futures markets. Features real-time signal generation, automated order execution via exchange API, backtesting, and advanced risk management.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>Pandas</li>
                  <li>Backtrader</li>
                  <li>REST API</li>
                  <li>Docker</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal4">Learn More</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/risk-dashboard.jpg" alt="Risk Management Dashboard" class="img-fluid">
              <div class="portfolio-content">
                <h4>Risk Management Dashboard</h4>
                <p>Comprehensive risk monitoring system with real-time alerts and position management features.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>Vue.js</li>
                  <li>Redis</li>
                  <li>Kubernetes</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal5">Learn More</a>
              </div>
            </div>
          </div>
          <div class="col-md-6 mb-4">
            <div class="portfolio-item">
              <img src="assets/images/projects/feature-engineering.jpg" alt="Feature Engineering Pipeline" class="img-fluid">
              <div class="portfolio-content">
                <h4>Feature Engineering Pipeline</h4>
                <p>Automated feature generation and selection pipeline for financial time series prediction.</p>
                <ul class="tech-stack">
                  <li>Python</li>
                  <li>Spark</li>
                  <li>AWS</li>
                  <li>Airflow</li>
                </ul>
                <a href="#" class="filled-button" data-modal="modal6">Learn More</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <div id="modal1" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Crypto Analytics Dashboard</h4>
        <p><strong>Objective:</strong> To develop a real-time cryptocurrency trading dashboard that provides actionable insights through market sentiment analysis and automated trading signals.</p>
        <p><strong>Methodology:</strong> The system ingests real-time data from multiple cryptocurrency exchanges and social media sources. It uses natural language processing (NLP) to analyze market sentiment and a set of predefined rules to generate trading signals. The front-end is a responsive React application that visualizes the data and signals.</p>
        <p><strong>Results:</strong> The dashboard provides a comprehensive overview of the crypto market, helping users make informed trading decisions. The automated signals have shown a consistent profitability rate in backtesting.</p>
        <p><strong>Challenges:</strong> The main challenges were handling the high volume of real-time data and developing an accurate sentiment analysis model.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python (for data processing and analysis)</li>
          <li>React (for the front-end dashboard)</li>
          <li>WebSocket (for real-time data streaming)</li>
          <li>MongoDB (for data storage)</li>
        </ul>
        <a href="https://github.com/josetraderx" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <div id="modal2" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Portfolio Analytics Engine</h4>
        <p><strong>Objective:</strong> To build a powerful portfolio optimization and analysis tool using quantitative methods.</p>
        <p><strong>Methodology:</strong> The engine implements Modern Portfolio Theory (MPT) to find the optimal allocation for a basket of assets. It also uses machine learning models to forecast asset returns and volatility.</p>
        <p><strong>Results:</strong> The system is able to construct portfolios with a higher Sharpe ratio compared to traditional methods. The ML models provide more accurate forecasts, leading to better risk-adjusted returns.</p>
        <p><strong>Challenges:</strong> A key challenge was to create a flexible and extensible architecture that could easily incorporate new optimization algorithms and asset classes.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python</li>
          <li>NumPy</li>
          <li>Pandas</li>
          <li>Scikit-learn</li>
        </ul>
        <a href="https://github.com/josetraderx" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <div id="modal3" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Backtesting Framework</h4>
        <p><strong>Objective:</strong> To create a robust and efficient backtesting framework for evaluating quantitative trading strategies.</p>
        <p><strong>Methodology:</strong> The framework is built in Python and uses a vectorized approach for fast backtesting. It supports various types of trading strategies and provides detailed performance metrics and visualizations.</p>
        <p><strong>Results:</strong> The backtesting engine is significantly faster than traditional loop-based backtesters. It has enabled the rapid iteration and validation of new trading ideas.</p>
        <p><strong>Challenges:</strong> The main difficulty was to design a system that is both high-performance and flexible enough to accommodate a wide range of trading strategies.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python</li>
          <li>Pandas</li>
          <li>Plotly</li>
          <li>FastAPI</li>
        </ul>
        <a href="https://github.com/josetraderx" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <div id="modal4" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Moving Average Trading Bot</h4>
        <p><strong>Objective:</strong> To develop an automated trading bot based on the moving average crossover strategy.</p>
        <p><strong>Methodology:</strong> The bot continuously monitors the price of a given asset and executes trades when the short-term moving average crosses the long-term moving average. It uses the Backtrader library for backtesting and live trading.</p>
        <p><strong>Results:</strong> The trading bot has been deployed and is operating profitably. It has demonstrated the effectiveness of the moving average strategy in certain market conditions.</p>
        <p><strong>Challenges:</strong> The main challenge was to implement a reliable and fault-tolerant system that can run 24/7 without manual intervention.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python</li>
          <li>Pandas</li>
          <li>Backtrader</li>
          <li>REST API</li>
          <li>Docker</li>
        </ul>
        <a href="https://github.com/josetraderx/Algorithmic-Trading-Bot-Moving-Average-Crossover-Strategy.git" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <div id="modal5" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Risk Management Dashboard</h4>
        <p><strong>Objective:</strong> To build a real-time risk management dashboard for a trading desk.</p>
        <p><strong>Methodology:</strong> The dashboard provides a comprehensive view of the trading desk's positions and risk exposure. It calculates various risk metrics in real-time and sends alerts when risk limits are breached.</p>
        <p><strong>Results:</strong> The risk dashboard has improved the trading desk's ability to monitor and control risk. It has helped to prevent significant losses during periods of high market volatility.</p>
        <p><strong>Challenges:</strong> The main challenge was to develop a scalable and low-latency system that can handle a large number of positions and real-time market data.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python</li>
          <li>Vue.js</li>
          <li>Redis</li>
          <li>Kubernetes</li>
        </ul>
        <a href="https://github.com/josetraderx" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <div id="modal6" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h4>Feature Engineering Pipeline</h4>
        <p><strong>Objective:</strong> To automate the process of feature engineering for financial machine learning models.</p>
        <p><strong>Methodology:</strong> The pipeline takes raw financial data as input and generates a rich set of features using various transformation techniques. It also includes a feature selection module that selects the most predictive features for a given model.</p>
        <p><strong>Results:</strong> The feature engineering pipeline has significantly reduced the time and effort required to develop new machine learning models. It has also improved the performance of the models by providing them with high-quality features.</p>
        <p><strong>Challenges:</strong> The main challenge was to design a flexible and extensible pipeline that can support a wide range of feature engineering techniques and be easily integrated with different machine learning models.</p>
        <h5>Technologies Used:</h5>
        <ul>
          <li>Python</li>
          <li>Spark</li>
          <li>AWS</li>
          <li>Airflow</li>
        </ul>
        <a href="https://github.com/josetraderx" class="filled-button" target="_blank">View on GitHub</a>
      </div>
    </div>

    <!-- Footer Starts Here -->
    <footer>
      <div class="container">
        <div class="row">
          <div class="col-md-3 footer-item">
            <h4>Your Quant Partner</h4>
            <p>Quantitative Developer & Data Scientist focused on algorithmic trading, predictive analytics, and data-driven investment solutions.</p>
            <ul class="social-icons">
              <li><a rel="nofollow" href="https://www.linkedin.com/in/josetraderx/" target="_blank"><i class="fa fa-linkedin"></i></a></li>
              <li><a rel="nofollow" href="https://github.com/josetraderx" target="_blank"><i class="fa fa-github"></i></a></li>
              <li><a rel="nofollow" href="https://www.instagram.com/josetraderx/" target="_blank"><i class="fa fa-instagram"></i></a></li>
            </ul>
          </div>
          <div class="col-md-3 footer-item">
            <h4>Useful Links</h4>
            <ul class="menu-list">
              <li><a href="services.html">Services</a></li>
              <li><a href="about.html">About Me</a></li>
              <li><a href="https://www.linkedin.com/in/josetraderx" target="_blank">LinkedIn</a></li>
              <li><a href="https://github.com/josetraderx" target="_blank">GitHub</a></li>
              <li><a href="contact.html">Contact</a></li>
            </ul>
          </div>
          <div class="col-md-3 footer-item">
            <h4>Additional Pages</h4>
            <ul class="menu-list">
              <li><a href="trading-strategies.html">Trading Strategies</a></li>
              <li><a href="#"><em>Market Analysis</em> (Coming Soon)</a></li>
              <li><a href="#"><em>Terms of Service</em> (Coming Soon)</a></li>
            </ul>
          </div>
          <div class="col-md-3 footer-item last-item">
            <h4>Contact</h4>
            <div class="contact-form">
              <form id="footer-contact" action="https://formsubmit.co/<EMAIL>" method="POST">
                <input type="hidden" name="_captcha" value="false">
                <input type="hidden" name="_next" value="portfolio.html">
                <div class="row">
                  <div class="col-lg-12 col-md-12 col-sm-12">
                    <fieldset>
                      <input name="name" type="text" class="form-control" id="name" placeholder="Full Name" required="">
                    </fieldset>
                  </div>
                  <div class="col-lg-12 col-md-12 col-sm-12">
                    <fieldset>
                      <input name="email" type="text" class="form-control" id="email" pattern="[^ @]*@[^ @]*" placeholder="E-Mail Address" required="">
                    </fieldset>
                  </div>
                  <div class="col-lg-12">
                    <fieldset>
                      <textarea name="message" rows="6" class="form-control" id="message" placeholder="Your Message" required=""></textarea>
                    </fieldset>
                  </div>
                  <div class="col-lg-12">
                    <fieldset>
                      <button type="submit" id="form-submit" class="filled-button">Send Message</button>
                    </fieldset>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </footer>
    
    <div class="sub-footer">
      <div class="container">
        <div class="row">
          <div class="col-md-12">
            <p>Copyright &copy; 2025 José TraderX - Template Design: <a rel="nofollow noopener" href="https://templatemo.com" target="_blank">TemplateMo</a></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap core JavaScript -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Additional Scripts -->
    <script src="assets/js/custom.js"></script>
    <script src="assets/js/owl.js"></script>
    <script src="assets/js/slick.js"></script>
    <script src="assets/js/accordions.js"></script>

    <script>
      // Get all modals
      var modals = document.querySelectorAll('.modal');

      // Get all buttons that open a modal
      var btns = document.querySelectorAll('[data-modal]');

      // Get all elements that close a modal
      var spans = document.querySelectorAll('.close');

      // When the user clicks the button, open the corresponding modal 
      btns.forEach(function(btn) {
        btn.onclick = function(e) {
          e.preventDefault();
          var modal = document.getElementById(btn.getAttribute('data-modal'));
          modal.style.display = "block";
        }
      });

      // When the user clicks on <span> (x), close the modal
      spans.forEach(function(span) {
        span.onclick = function() {
          var modal = span.closest('.modal');
          modal.style.display = "none";
        }
      });

      // When the user clicks anywhere outside of the modal, close it
      window.onclick = function(event) {
        if (event.target.classList.contains('modal')) {
          event.target.style.display = "none";
        }
      }
    </script>

  </body>
</html>
